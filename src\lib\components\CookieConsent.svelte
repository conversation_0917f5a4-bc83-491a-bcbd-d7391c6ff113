<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { writable } from 'svelte/store';

	const COOKIE_NAME = 'cookie_consent';
	const COOKIE_MAX_AGE = 60 * 60 * 24 * 365; // 1 year in seconds

	const visible = writable(false);

	onMount(() => {
		if (!browser) return;

		const hasConsent = document.cookie.split('; ').find((row) => row.startsWith(`${COOKIE_NAME}=`));

		visible.set(!hasConsent);
	});

	function acceptCookies() {
		if (!browser) return;

		document.cookie = `${COOKIE_NAME}=1; max-age=${COOKIE_MAX_AGE}; path=/`;
		visible.set(false);
	}
</script>

{#if $visible}
	<div
		class="fixed inset-x-0 bottom-0 z-50 flex flex-col items-center justify-between gap-4 bg-gray-800 p-4 text-sm text-white shadow-lg md:flex-row"
	>
		<div class="flex-1 text-center md:text-left">
			This site uses essential cookies only.
			<a href="/privacy-policy" class="ml-1 text-blue-300 underline hover:text-blue-400"
				>See our privacy policy</a
			> for details.
		</div>
		<button on:click={acceptCookies} class="btn-base-100 btn btn-sm text-base-content">
			Dismiss
		</button>
	</div>
{/if}
