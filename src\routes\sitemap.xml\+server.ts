import type { RequestHandler } from '@sveltejs/kit';

const baseUrl = 'https://www.jdes.co.uk';

export const GET: RequestHandler = async () => {
	const pages = [
		'',
		'/about-us',
		'/agricultural',
		'/areas-we-cover',
		'/commercial-industrial',
		'/contact-us',
		'/domestic',
		'/showcase',
		'/privacy-policy',
		'/sustainability-resiliency-efficiency',
		'/terms-and-conditions',
		'/why-choose-us'
	];

	const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages
	.map(
		(page) => `	<url>
		<loc>${baseUrl}${page}</loc>
		<lastmod>${new Date().toISOString()}</lastmod>
		<changefreq>weekly</changefreq>
		<priority>${page === '' ? '1.0' : '0.8'}</priority>
	</url>`
	)
	.join('\n')}
</urlset>`;

	return new Response(sitemap, {
		headers: {
			'Content-Type': 'application/xml',
			'Cache-Control': 'max-age=0, s-maxage=3600'
		}
	});
};
